﻿using UNI.Model;
using UNI.Master.Model.UniMaster;
using System;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.DAL.Interfaces;

namespace UNI.Master.BLL.BusinessService
{
    public class OrderDetailService : IOrderDetailService
    {
        private readonly IOrderDetailRepository _orderDetailRepository;
        public OrderDetailService(IOrderDetailRepository orderDetailRepository)
        {
            if (orderDetailRepository != null)
                _orderDetailRepository = orderDetailRepository;
        }
        public Task<CommonListPage> GetOrderDetailPage(FilterOrderDetail flt)
        {
            return _orderDetailRepository.GetOrderDetailPage(flt);
        }
        public Task<OrderDetailInfo> GetOrderDetailInfo(string userId, Guid? id)
        {
            return _orderDetailRepository.GetOrderDetailInfo(userId, id);
        }
        public Task<BaseValidate> SetOrderDetailInfo(string userId, OrderDetailInfo prod)
        {
            return _orderDetailRepository.SetOrderDetailInfo(userId, prod);
        }
        public Task<BaseValidate> DelOrderDetailInfo(string userId, Guid id)
        {
            return _orderDetailRepository.DelOrderDetailInfo(userId, id);
        }
    }
}
