﻿using System;
using System.Globalization;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace UNI.Utils
{
    public static class VitualAccount
    {
        public static string GenerateVirtual(string shortName, string prefix, string virtualPartNum)
        {
            if(shortName.ToUpper() == "MB")
            {
                return GenerateVirtualAccountMB(prefix, virtualPartNum);
            }
            else if(shortName.ToUpper() == "KLB")
            {
                 return GenerateVirtualAccount(prefix, virtualPartNum);
            }
            return GenerateVirtualAccount(prefix, virtualPartNum);
        }
        public static string GenerateVirtualAccountMB(string prefix, string virtualPartNum)
        {
            // Tạo tài khoản ảo
            StringBuilder res = new StringBuilder();
            res.Append(prefix);
            res.Append(virtualPartNum);

            //// Thêm chữ số kiểm tra (sử dụng hàm tính chữ số kiểm tra)
            //res.Append(GetCheckDigit(res.ToString()));

            return res.ToString();
        }
        public static string GenerateVirtualAccount(string prefix, string virtualPartNum)
        {
            // Tạo tài khoản ảo
            StringBuilder res = new StringBuilder();
            res.Append(prefix);
            res.Append(virtualPartNum);

            // Thêm chữ số kiểm tra (sử dụng hàm tính chữ số kiểm tra)
            res.Append(GetCheckDigit(res.ToString()));

            return res.ToString();
        }

        // Hàm tính chữ số kiểm tra, dựa vào đoạn mã đã chuyển đổi trước đó
        private static int GetCheckDigit(string number)
        {
            int sum = 0;

            for (int i = 0; i < number.Length; i++)
            {
                int digit = int.Parse(number.Substring(i, 1));

                if (i % 2 == 0)
                {
                    digit *= 2;
                    if (digit > 9)
                    {
                        digit = (digit / 10) + (digit % 10);
                    }
                }

                sum += digit;
            }

            int mod = sum % 10;
            return (mod == 0) ? 0 : 10 - mod;
        }

    }

}

