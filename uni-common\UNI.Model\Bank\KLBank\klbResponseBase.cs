﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UNI.Model.Bank.KLBank
{
    public class klbResponseBase
    {
        public int code { get; set; }
        public string message { get; set; }
        public string data { get; set; }
    }
    public class mbResponseBase
    {
        public string requestId { get; set; }
        public AccountData data { get; set; }
        public string signature { get; set; }
    }
    public class AccountData
    {
        public string customerAcc { get; set; }
        public string customerName { get; set; }
        public string rate { get; set; }
        public string responseCode { get; set; }
        public string responseDesc { get; set; }
    }
    public class OrderUpdateRequest
    {
        public string requestId { get; set; }
        public OrderData data { get; set; }
        public string signature { get; set; } // chữ ký MB gửi (referenceNumber+customerAcc+amount+transDate)
    }
    public class OrderData
    {
        public string referenceNumber { get; set; }
        public string amount { get; set; }
        public string customerAcc { get; set; }
        public string transDate { get; set; }
        public string billNumber { get; set; }
        public string endPointUrl { get; set; }
        public string userName { get; set; }
        public string rate { get; set; }
        public string customerName { get; set; }
        public List<AdditionalDataItem> additionalData { get; set; }
    }

    public class AdditionalDataItem
    {
        public string name { get; set; }
        public string value { get; set; }
    }

    public class OrderUpdateResponse
    {
        public string requestId { get; set; }
        public OrderResponseData data { get; set; }
        public string signature { get; set; } // chữ ký partner (transactionId+responseCode)
    }

    public class OrderResponseData
    {
        public string transactionId { get; set; }
        public string responseCode { get; set; } // "00", "01", "02"
        public string responseDesc { get; set; }
    }
}
