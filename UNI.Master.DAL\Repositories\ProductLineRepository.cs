﻿using Dapper;
using Microsoft.Extensions.Configuration;
using UNI.Model;
using UNI.Master.Model.UniMaster;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using UNI.Master.DAL.Interfaces;

namespace UNI.Master.DAL.Repositories
{
    /// <summary>
    /// WebRoleRepository 
    /// </summary>
    /// Author: 
    /// CreatedDate: 16/11/2016 2:07 PM
    /// <seealso cref="ProductLineRepository" />
    public class ProductLineRepository : IProductLineRepository
    {
        private readonly string _connectionString;

        public ProductLineRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("dbUniMasterConnection");
        }
               
        public async Task<CommonListPage> GetProductLinePage(FilterInput flt)
        {
            const string storedProcedure = "sp_product_line_page";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                param.Add("@UserId", flt.userId);
                param.Add("@filter", flt.filter);

                param.Add("@Offset", flt.offSet);
                param.Add("@PageSize", flt.pageSize);
                param.Add("@Total", 0, DbType.Int64, ParameterDirection.InputOutput);
                param.Add("@TotalFiltered", 0, DbType.Int64, ParameterDirection.InputOutput);

                var result = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
                var data = new CommonListPage();
                if (flt.offSet == null || flt.offSet == 0)
                {
                    data.gridflexs = result.Read<viewGridFlex>().ToList();
                }
                var datalist = result.Read<object>().ToList();
                data.dataList = new ResponseList<List<object>>(datalist, param.Get<long>("@Total"), param.Get<long>("@TotalFiltered"));
                return data;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<productLineInfo> GetProductLineInfo(string userId, Guid? id)
        {
            const string storedProcedure = "sp_product_line_fields";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                param.Add("@userId", userId);
                param.Add("@id", id);
                var result = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
                var data = result.ReadFirstOrDefault<productLineInfo>();
                if (data != null)
                {
                    data.group_fields = result.Read<viewGroup>().ToList();
                    if (data.group_fields != null && data.group_fields.Count > 0)
                    {
                        var flds = result.Read<viewField>().ToList();
                        if (flds.Count > 0)
                        {
                            foreach (var gr in data.group_fields)
                            {
                                gr.fields = flds.Where(f => f.group_cd == gr.group_cd).ToList();
                            }
                        }
                    }

                }
                return data;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<BaseValidate> SetProductLineInfo(string userId, productLineInfo query)
        {
            const string storedProcedure = "sp_product_line_set";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                param.Add("@userId", userId);
                param.Add("@id", query.id);
                param.AddDynamicParams(query.ToObject());
                var result = await connection.QueryFirstAsync<BaseValidate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                return result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<BaseValidate> DelProductLineInfo(string userId, Guid id)
        {
            const string storedProcedure = "sp_product_line_del";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                param.Add("@userId", userId);
                param.Add("@id", id);
                var result = await connection.QueryFirstAsync<BaseValidate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                return result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<List<CommonValue>> GetProducLineParent(string userId, Guid? id)
        {
            const string storedProcedure = "sp_product_line_parent";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                //param.Add("@userId", userId);
                param.Add("@id", id);
                var result = connection.Query<CommonValue>(storedProcedure, param, commandType: CommandType.StoredProcedure).ToList();
                return result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<List<CommonValue>> GetProducLineList(string userId, Guid? id)
        {
            const string storedProcedure = "sp_product_line_list";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                //param.Add("@userId", userId);
                param.Add("@id", id);
                var result = connection.Query<CommonValue>(storedProcedure, param, commandType: CommandType.StoredProcedure).ToList();
                return result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<CommonListPage> GetProductCategoryPage(FilterBase flt)
        {
            const string storedProcedure = "sp_product_category_page";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                param.Add("@UserId", flt.userId);
                param.Add("@filter", flt.filter);
                param.Add("@id", flt.id);
                param.Add("@Offset", flt.offSet);
                param.Add("@PageSize", flt.pageSize);
                param.Add("@Total", 0, DbType.Int64, ParameterDirection.InputOutput);
                param.Add("@TotalFiltered", 0, DbType.Int64, ParameterDirection.InputOutput);

                var result = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
                var data = new CommonListPage();
                if (flt.offSet == null || flt.offSet == 0)
                {
                    data.gridflexs = result.Read<viewGridFlex>().ToList();
                }
                var datalist = result.Read<object>().ToList();
                data.dataList = new ResponseList<List<object>>(datalist, param.Get<long>("@Total"), param.Get<long>("@TotalFiltered"));
                return data;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<BaseValidate> SetProductCategoryInfo(string userId, productLineInfo prod)
        {
            const string storedProcedure = "sp_product_category_set";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                param.Add("@userId", userId);
                param.Add("@id", prod.id);
                param.AddDynamicParams(prod.ToObject());
                var result = await connection.QueryFirstAsync<BaseValidate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                return result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<productLineInfo> GetProductCategoryInfo(string userId, Guid? id, Guid? prod_line_id)
        {
            const string storedProcedure = "sp_product_category_fields";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                param.Add("@userId", userId);
                param.Add("@id", id);
                param.Add("@prod_line_id", prod_line_id);
                var result = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
                var data = result.ReadFirstOrDefault<productLineInfo>();
                if (data != null)
                {
                    data.group_fields = result.Read<viewGroup>().ToList();
                    if (data.group_fields != null && data.group_fields.Count > 0)
                    {
                        var flds = result.Read<viewField>().ToList();
                        if (flds.Count > 0)
                        {
                            foreach (var gr in data.group_fields)
                            {
                                gr.fields = flds.Where(f => f.group_cd == gr.group_cd).ToList();
                            }
                        }
                    }
                }
                return data;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<BaseValidate> DelProductCategoryInfo(string userId, Guid id)
        {
            const string storedProcedure = "sp_product_category_del";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                param.Add("@userId", userId);
                param.Add("@id", id);
                var result = await connection.QueryFirstAsync<BaseValidate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                return result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<List<CommonValue>> GetProductCategoryList(string userId, string filter)
        {
            const string storedProcedure = "sp_product_category_list";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                //param.Add("@userId", userId);
                param.Add("@filter", filter);
                var result = connection.Query<CommonValue>(storedProcedure, param, commandType: CommandType.StoredProcedure).ToList();
                return result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

    }
}
