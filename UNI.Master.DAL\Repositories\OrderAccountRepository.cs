﻿using Dapper;
using Microsoft.Extensions.Configuration;
using UNI.Model;
using UNI.Master.Model.UniMaster;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Threading.Tasks;
using UNI.Master.DAL.Interfaces;
using System.Linq;

namespace UNI.Master.DAL.Repositories
{
    public class OrderAccountRepository : IOrderAccountRepository
    {
        private readonly string _connectionString;
        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="configuration"></param>
        public OrderAccountRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("dbUniMasterConnection");
        }
        public async Task<CommonListPage> GetOrderAccountPage(FilterOrderAccount flt)
        {
            const string storedProcedure = "sp_order_account_get";

            await using SqlConnection connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            var param = new DynamicParameters();
            param.Add("@UserId", flt.userId);
            param.Add("@filter", flt.filter);
            param.Add("@product_id", flt.pro_id);

            param.Add("@Offset", flt.offSet);
            param.Add("@PageSize", flt.pageSize);
            param.Add("@Total", 0, DbType.Int64, ParameterDirection.InputOutput);
            param.Add("@TotalFiltered", 0, DbType.Int64, ParameterDirection.InputOutput);

            var result = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
            var data = new CommonListPage();
            if (flt.offSet == null || flt.offSet == 0)
            {
                data.gridflexs = result.Read<viewGridFlex>().ToList();
            }
            var orders = result.Read<object>().ToList();
            data.dataList = new ResponseList<List<object>>(orders, param.Get<long>("@Total"), param.Get<long>("@TotalFiltered"));
            return data;
        }

        public async Task<OrderAccountInfo> GetOrderAccountInfo(string userId, Guid? id)
        {
            const string storedProcedure = "sp_order_account_fields";
            await using SqlConnection connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            var param = new DynamicParameters();
            param.Add("@userId", userId);
            param.Add("@id", id);
            var result = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
            var data = result.ReadFirstOrDefault<OrderAccountInfo>();
            if (data != null)
            {
                data.group_fields = result.Read<viewGroup>().ToList();
                if (data.group_fields != null && data.group_fields.Count > 0)
                {
                    var flds = result.Read<viewField>().ToList();
                    if (flds.Count > 0)
                    {
                        foreach (var gr in data.group_fields)
                        {
                            gr.fields = flds.Where(f => f.group_cd == gr.group_cd).ToList();
                        }
                    }
                }

            }
            return data;
        }

        public async Task<BaseValidate> SetOrderAccountInfo(string userId, OrderAccountInfo role)
        {
            const string storedProcedure = "sp_order_account_set";
            await using SqlConnection connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            var param = new DynamicParameters();
            param.Add("@userId", userId);
            param.Add("@id", role.id);
            param.Add("@ord_id", role.GetValueByFieldName("ord_id"));
            param.Add("@login_uni_id", role.GetValueByFieldName("login_uni_id"));
            param.Add("@login_uni_pass", role.GetValueByFieldName("login_uni_pass"));
            param.Add("@login_uni_alias", role.GetValueByFieldName("login_uni_alias"));
            param.Add("@user_id", role.GetValueByFieldName("user_id"));

            var result = await connection.QueryFirstAsync<BaseValidate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
            return result;
        }
        public async Task<BaseValidate> DelOrderAccountInfo(string userId, Guid id)
        {
            const string storedProcedure = "sp_order_account_del";
            await using SqlConnection connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();
            var param = new DynamicParameters();
            param.Add("@userId", userId);
            param.Add("@id", id);
            var result = await connection.QueryFirstAsync<BaseValidate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
            return result;
        }
    }
}
