﻿using UNI.Model;
using UNI.Master.Model.UniMaster;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FilterOrder = UNI.Master.Model.UniMaster.FilterOrder;

namespace UNI.Master.DAL.Interfaces
{

    public interface IOrderRepository
    {
        Task<CommonListPage> GetOrderPage(FilterOrder flt);
        Task<OrderInfo> GetOrderInfo(string userId, Guid? id);
        Task<BaseValidate> SetOrderInfo(string userId, OrderInfo role);
        Task<BaseValidate> DelOrderInfo(string userId, Guid id);
        Task<List<CommonValue>> GetOrderList(string userId, string filter);

    }
}
