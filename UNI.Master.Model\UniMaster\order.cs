﻿using UNI.Model;
using System;

namespace UNI.Master.Model.UniMaster
{

    
    public class OrderInfo : viewBaseInfo
    {
        public Guid? Id { get; set; }
    }
    public class FilterOrder : FilterBase
    {
        public Guid? cust_id { get; set; }
        public FilterOrder(string clientid, string userid, int? offset, int? pagesize, string filter, int gridwidth, Guid? cust_id) : base(clientid, userid, offset, pagesize, filter, gridwidth)
        {
            this.cust_id = cust_id;
        }
    }
    public class OrderDetailInfo : viewBaseInfo
    {
        public Guid? id { get; set; }
    }
    public class FilterOrderDetail : FilterBase
    {
        public Guid? ord_id { get; set; }
        public FilterOrderDetail(string clientid, string userid, int? offset, int? pagesize, string filter, int gridwidth, Guid? ord_id) : base(clientid, userid, offset, pagesize, filter, gridwidth)
        {
            this.ord_id = ord_id;
        }
    }
    public class OrderAccountInfo : viewBaseInfo
    {
        public Guid? id { get; set; }
    }
    public class FilterOrderAccount : FilterBase
    {
        public Guid? pro_id { get; set; }
        public FilterOrderAccount(string clientid, string userid, int? offset, int? pagesize, string filter, int gridwidth, Guid? pro_id) : base(clientid, userid, offset, pagesize, filter, gridwidth)
        {
            this.pro_id = pro_id;
        }
    }

}
