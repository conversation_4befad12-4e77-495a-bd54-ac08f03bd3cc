﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.Model.UniMaster;
using UNI.Model;
using UNI.Model.Api;
using FilterOrder = UNI.Master.Model.UniMaster.FilterOrder;

namespace UNI.Master.API.Controllers.Version1
{

    /// <summary>
    ///
    /// </summary>
    /// Author: duongpx
    /// CreatedDate: 07/04/2020 9:31 AM
    /// <seealso cref="OrderController" />
    [Route("api/v1/order/[action]")]
    [Authorize]
    public class OrderController : UniController
    {

        private readonly IOrderService _orderService;
        private readonly ISysManageService _systemService;

        /// <summary>
        /// Web Controller
        /// </summary>
        /// <param name="orderService"></param>
        /// <param name="appSettings"></param>
        /// <param name="logger"></param>
        public OrderController(
            IOrderService orderService,
             ISysManageService systemService,
            IOptions<AppSettings> appSettings,
            ILoggerFactory logger) : base(appSettings, logger)
        {
            _orderService = orderService;
        }
        /// <summary>
        /// GetOrderFilter - Filter
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonViewInfo>> GetOrderFilter()
        {
            var result = await _systemService.GetManagerFilter(this.UserId, "mas_order_filter");
            return GetResponse<CommonViewInfo>(ApiResult.Success, result);
        }
        /// <summary>
        /// GetOrderPage
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="gridWith"></param>
        /// <param name="offSet"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetOrderPage(
            [FromQuery] string filter,
            [FromQuery] Guid? cust_id,
            [FromQuery] int gridWith,
            [FromQuery] int offSet,
            [FromQuery] int pageSize)
        {
            var flt = new FilterOrder(this.ClientId, this.UserId, offSet, pageSize, filter, gridWith, cust_id);
            var result = await _orderService.GetOrderPage(flt);
            return GetResponse<CommonListPage>(ApiResult.Success, result);
        }
        /// <summary>
        /// GetProductInfo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<OrderInfo>> GetOrderInfo([FromQuery] Guid? id)
        {
            var result = await _orderService.GetOrderInfo(this.UserId, id);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// SetProductInfo
        /// </summary>
        /// <param name="prod"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<string>> SetOrderInfo([FromBody] OrderInfo prod)
        {
            var result = await _orderService.SetOrderInfo(this.UserId, prod);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
        /// <summary>
        /// DelProductInfo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<BaseResponse<string>> DelOrderInfo([FromQuery] Guid id)
        {
            var result = await _orderService.DelOrderInfo(this.UserId, id);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
        /// <summary>
        /// GetProductList - Danh sách sản phẩm
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<CommonValue>>> GetOrderList([FromQuery] string customer_id, [FromQuery] string filter)
        {
            var result = await _orderService.GetOrderList(customer_id, filter);
            return GetResponse(ApiResult.Success, result);
        }


    }
}

