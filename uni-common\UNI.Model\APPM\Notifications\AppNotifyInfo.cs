﻿using System;
using System.Collections.Generic;

namespace UNI.Model.APPM.Notifications
{
    public class AppNotifyInfo : viewBaseInfo
    {
        public long notiId { get; set; }
        public Guid? n_id { get; set; }
        public string external_sub { get; set; }
        public List<AppNotifyAttach> attachs { get; set; }
    }
    public class AppNotifyId
    {
        public Guid n_id { get; set; }
        public bool status { get; set; }
    }
    
    public class AppNotifyAttach
    {
        public long? id { get; set; }
        public long notiId { get; set; }
        public string attach_name { get; set; }
        public string attach_url { get; set; }
        public string attach_type { get; set; }
    }
    public class AppNotifyAttach1
    {
        public Guid? n_id { get; set; }
        public Guid? groupFileId { get; set; }
        public string attach_name { get; set; }
        public string attach_size { get; set; }
        public string attach_url { get; set; }
        public string attach_type { get; set; }
    }
    public class AppNotifyParam
    {
        public Guid? n_id { get; set; }
        public Guid? tempId { get; set; }
        public string external_key { get; set; }
        public string external_sub { get; set; }
        public string external_name { get; set; }
        public string brand_name { get; set; }
        public string send_name { get; set; }
    }
    public class NotifyInfoSet : viewBaseInfo
    {
        public Guid? n_id { get; set; }
        public string external_sub { get; set; }
        public int to_count { get; set; }
        public int? to_level { get; set; }
        public string to_groups { get; set; }
        public List<NotifyToLevel> toLevels { get; set; }
        public List<NotifyToSet> notifyTos { get; set; }
        public bool sendNow { get; set; }
    }
    public class NotifyInfo : viewBaseInfo
    {
        public Guid? n_id { get; set; }
        public string external_sub { get; set; }
        public int to_count { get; set; }
        public int? to_level { get; set; }
        public string to_groups { get; set; }
        public List<NotifyToGet> notifyTos { get; set; }
        public List<NotifyToLevel> toLevels { get; set; }
    }
    public class NotifyToLevel
    {
        public string to_level { get; set; }
        public string to_name { get; set; }
        public string columnType { get; set; }
        public string columnObject { get; set; }
    }
    public class NotifyParam
    {
        public Guid? n_id { get; set; }
        public Guid? tempId { get; set; }
        public Guid? source_ref { get; set; }
        public string actions { get; set; }
        public int to_type { get; set; }
        public int? to_level { get; set; }
        public string to_groups { get; set; }
        public string external_sub { get; set; }
    }

    public class NotifyToGet : NotifyToSet
    {
        public string to_name { get; set; }
        public string columnType { get; set; }
        public string columnObject { get; set; }
    }
    public class NotifyToSet
    {
        public Guid? id { get; set; }
        public string to_level { get; set; }
        public string to_groups { get; set; }
        public string to_row { get; set; }
        public string to_type { get; set; }
    }
}
