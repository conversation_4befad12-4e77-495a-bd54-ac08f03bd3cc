﻿using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.Configuration;
using UNI.Utils;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml;
using System.Xml.Serialization;
using UNI.Model;

namespace UNI.Common
{
    public static class Utils
    {
        public static List<FieldInfo> GetConstants(Type type)
        {
            var fieldInfos = type.GetFields(BindingFlags.Public |
                                                    BindingFlags.Static | BindingFlags.FlattenHierarchy);

            return fieldInfos.Where(fi => fi.IsLiteral && !fi.IsInitOnly).ToList();
        }

        public static string ConvertToUnSign(string s)
        {
            var stFormD = s.Normalize(NormalizationForm.FormD);
            var sb = new StringBuilder();
            foreach (var t in stFormD)
            {
                UnicodeCategory uc = CharUnicodeInfo.GetUnicodeCategory(t);
                if (uc != System.Globalization.UnicodeCategory.NonSpacingMark)
                {
                    sb.Append(t);
                }
            }

            sb = sb.Replace('Đ', 'D');
            sb = sb.Replace('đ', 'd');

            return sb.ToString().Normalize(NormalizationForm.FormD);
        }

        public static string GetContentType(string path)
        {
            var provider = new FileExtensionContentTypeProvider();
            if (!provider.TryGetContentType(path, out var contentType))
            {
                contentType = "application/octet-stream";
            }

            return contentType;
        }

        public static DirectoryInfo CreateFolder(string baseFolder)
        {
            try
            {
                var now = DateTime.Now;
                var yearName = now.ToString("yyyy");
                var monthName = now.ToString("MM");
                var dayName = now.ToString("dd");

                var folder = Path.Combine(baseFolder,
                    Path.Combine(yearName,
                        Path.Combine(monthName,
                            dayName)));

                return Directory.CreateDirectory(folder);
            }
            catch (Exception)
            {
                // ignored
            }

            return null;
        }

        public static string FileNameValid(string fileName, int? index = null)
        {
            try
            {
                var fileNameValid = fileName;
                if (index != null)
                {
                    fileNameValid = Path.Combine(Path.GetDirectoryName(fileName) ?? throw new InvalidOperationException(), $"{Path.GetFileNameWithoutExtension(fileName)}_{index}{Path.GetExtension(fileName)}");
                }

                return File.Exists(fileNameValid) ? FileNameValid(fileName, index != null ? ++index : 1) : fileNameValid;
            }
            catch (Exception)
            {
                // ignored
            }

            return null;
        }

        public static string GetHash(string input)
        {
            using (var sha1 = SHA1.Create())
            {
                var byteValue = Encoding.UTF8.GetBytes(input);
                var hash = sha1.ComputeHash(byteValue);
                return Convert.ToBase64String(hash);
            }
        }
        public static T DeserializeXml<T>(string xml)
        {
            var ser = new XmlSerializer(typeof(T));
            var stringReader = new StringReader(xml);
            var xmlReader = XmlReader.Create(stringReader,
                new XmlReaderSettings() {DtdProcessing = DtdProcessing.Prohibit, IgnoreWhitespace = true});

            var obj = (T)ser.Deserialize(xmlReader);

            stringReader.Dispose();
            xmlReader.Dispose();
            
            return obj;
        }

        //Serializes the <i>Obj</i> to an XML string.
        public static string SerializeXml<T>(object obj)
        {
            try
            {
                var ser = new XmlSerializer(typeof(T));
                var memStream = new MemoryStream();
                var xmlWriter = XmlWriter.Create(memStream, new XmlWriterSettings()
                {
                    Encoding = Encoding.UTF8
                });
                //var xmlWriter = new XmlTextWriter(memStream, Encoding.UTF8) { Namespaces = true };

                ser.Serialize(xmlWriter, obj);
                
                var xml = Encoding.UTF8.GetString(memStream.ToArray());

                xmlWriter.Dispose();
                memStream.Dispose();

                xml = xml.Substring(xml.IndexOf(Convert.ToChar(60)));
                xml = xml.Substring(0, (xml.LastIndexOf(Convert.ToChar(62)) + 1));

                return xml.Replace("<?xml version=\"1.0\" encoding=\"utf-8\"?>", "")
                    .Replace("xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"", "")
                    .Replace("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"", "");
            }
            catch (Exception)
            {
                // ignored
                throw;
            }
        }
        //Serializes the <i>Obj</i> to an XML string.
        public static string SerializeXml(object obj)
        {
            try
            {
                var ser = new XmlSerializer(obj.GetType());
                var memStream = new MemoryStream();
                var xmlWriter = XmlWriter.Create(memStream, new XmlWriterSettings()
                {
                    Encoding = Encoding.UTF8
                });
                //var xmlWriter = new XmlTextWriter(memStream, Encoding.UTF8) { Namespaces = true };

                ser.Serialize(xmlWriter, obj);
                
                var xml = Encoding.UTF8.GetString(memStream.ToArray());

                xmlWriter.Dispose();
                memStream.Dispose();

                xml = xml.Substring(xml.IndexOf(Convert.ToChar(60)));
                xml = xml.Substring(0, (xml.LastIndexOf(Convert.ToChar(62)) + 1));

                return xml.Replace("<?xml version=\"1.0\" encoding=\"utf-8\"?>", "")
                    .Replace("xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"", "")
                    .Replace("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"", "");
            }
            catch (Exception e)
            {
                // ignored
                throw e;
            }
        }

        public static string SerializeXmlWithNoSpace<T>(object obj)
        {
            // instantiate the container for all attribute overrides
            var xOver = new XmlAttributeOverrides();

            // define a set of XML attributes to apply to the root element
            var xAttrs1 = new XmlAttributes();

            // define an XmlRoot element (as if [XmlRoot] had decorated the type)
            // The namespace in the attribute override is the empty string. 
            var xRoot = new XmlRootAttribute() { Namespace = "" };

            // add that XmlRoot element to the container of attributes
            xAttrs1.XmlRoot = xRoot;

            // add that bunch of attributes to the container holding all overrides
            xOver.Add(typeof(T), xAttrs1);
            //Create our own namespaces for the output
            var ns = new XmlSerializerNamespaces();

            //Add an empty namespace and empty value
            ns.Add("", "");

            var ser = new XmlSerializer(typeof(T), xOver);
            using (var memStream = new MemoryStream())
            {
                var settings = new XmlWriterSettings
                {
                    OmitXmlDeclaration = true,
                    Encoding = Encoding.UTF8
                };
                //settings.Indent = true;
                using (var xmlWriter = XmlWriter.Create(memStream, settings))
                {
                    ser.Serialize(xmlWriter, obj, ns);
                }
                var xml = Encoding.UTF8.GetString(memStream.ToArray());
                xml = xml.Substring(xml.IndexOf(Convert.ToChar(60)));
                xml = xml.Substring(0, (xml.LastIndexOf(Convert.ToChar(62)) + 1));
                return xml;
            }
        }

        public static string CleanEmptyTags(string xml)
        {
            var regex = new Regex(@"(\s)*<(\w)*(\s)*/>", RegexOptions.None, TimeSpan.FromMilliseconds(3000));
            return regex.Replace(xml, string.Empty);
        }

        /// <summary>
        /// Serializes the object to XML based on encoding and name spaces.
        /// </summary>
        /// <param name="serializer">XmlSerializer object 
        /// (passing as param to avoid creating one every time)</param>
        /// <param name="encoding">The encoding of the serialized Xml</param>
        /// <param name="ns">The namespaces to be used by the serializer</param>
        /// <param name="omitDeclaration">Whether to omit Xml declarartion or not</param>
        /// <param name="objectToSerialize">The object we want to serialize to Xml</param>
        /// <returns></returns>
        public static string Serialize(XmlSerializer serializer,
                                       Encoding encoding,
                                       XmlSerializerNamespaces ns,
                                       bool omitDeclaration,
                                       object objectToSerialize)
        {
            var ms = new MemoryStream();
            var settings = new XmlWriterSettings
            {
                Indent = true,
                OmitXmlDeclaration = omitDeclaration,
                Encoding = encoding
            };
            var writer = XmlWriter.Create(ms, settings);
            serializer.Serialize(writer, objectToSerialize, ns);
            return encoding.GetString(ms.ToArray());
        }
        
        public static string RandomString(int size, bool lowerCase)
        {
            var randomNumberGenerator = RandomNumberGenerator.Create();
            var randomBytes = new byte[size];
            randomNumberGenerator.GetBytes(randomBytes);
            var chars = new char[size];
            for (int i = 0; i < size; i++)
            {
                // Generate a character from the ASCII range 65 ('A') to 90 ('Z')
                chars[i] = (char)('A' + randomBytes[i] % 26);
            }
            var result = new string(chars);
            return lowerCase ? result.ToLower() : result;
        }

        public static OrderType GetOrderType(string code)
        {
            var result = OrderType.Unknown;

            foreach (OrderType type in Enum.GetValues(typeof(OrderType)))
            {
                if (!code.StartsWith(type.GetDescription())) continue;
                result = type;
                break;
            }

            return result;
        }

        public static string GetOrderTypeName(string code)
        {
            var result = "";

            var orderType = GetOrderType(code);

            switch (orderType)
            {
                case OrderType.Booking:
                    result = "Booking";
                    break;
                case OrderType.Selling:
                    result = "Đặt mua";
                    break;
            }

            return result;
        }

        public static string GenerateId()
        {
            long i = 1;
            foreach (byte b in Guid.NewGuid().ToByteArray())
            {
                i *= ((int)b + 1);
            }
            return string.Format("{0:x}", i - DateTime.Now.Ticks);
        }
        //public static void BarCodeGenerator(string data, string path)
        //{
        //    Barcode b = new Barcode(data);
        //    BarcodeLib.TYPE type = TYPE.CODE128;
        //    BarcodeLib.SaveTypes saveType = SaveTypes.GIF;
        //    b.Alignment = AlignmentPositions.RIGHT;
        //    b.Encode(type, data, 300, 122);
        //    b.SaveImage(path, saveType);
        //}
        //public static string GetThumbImage(string imgUrl)
        //{
        //    if (!imgUrl.StartsWith("http://sunshinegroup.vn"))
        //    {
        //        int pos = imgUrl.LastIndexOf('/');
        //        if (pos > 0)
        //            return imgUrl.Substring(0, pos + 1) + "thumb_" + imgUrl.Substring(pos + 1);
        //        else
        //            return imgUrl;
        //    }
        //    else
        //        return imgUrl;
        //}
        //public static string GetHtmlFormat(string title, string description, string content)
        //{
        //    string strTitlehtml = string.Format("<h3>{0}</h3>", title);
        //    string strDesciption = string.Format("<h4>{0}</h4>", description);
        //    string strHtml = WebUtility.HtmlDecode(content);

        //    strHtml = strTitlehtml + strDesciption + strHtml;
        //    strHtml = Utils.InsertImageUrl(strHtml);
        //    strHtml = Utils.InsertHeadHtml(strHtml);
        //    return strHtml ;
        //}
        //public static string RemoveHeightHTML(string input)
        //{
        //    return Regex.Replace(input, "height:.*?px", String.Empty);
        //}
        //public static string RemoveWidthHTML(string input)
        //{
        //    return Regex.Replace(input, "width::.*?px", String.Empty);
        //}
        //public static string ImageResponsive(string source)
        //{
        //    return source.Replace("<img ", "<img class=\"img-responsive\" ");
        //}
        public static string InsertHeadHtml(string source)
        {
            string headhtml = "<html lang=\"en\">" +
                "<head>" +
                "<meta name = \"viewport\" content = \"width=device-width, initial-scale=1\">" +
                "<meta content = \"text/html\"; charset=UTF-8\" http-equiv = \"Content-Type\"/>" +
                "<style>" +
                "img {" +
                     "max-width: 100%!important;" +
                     "height: auto!important;" +
                     "display: block!important;" +
                "}" +
                "table, td {width: 100%!important;}," +
                "</style>" +
                "</head>" +
                "<body>" +
                "<div class=\"container\">" +
                source +
                "</div>" +
                "</body>" +
                "</html>";
            return headhtml;
        }
        public static string InsertImageUrl(string source)
        {
            return source.Replace("src=\"/Portals", "src=\"http://sunshinegroup.vn//Portals");
        }

        public static void SetEnvironmentVariable(bool isDevelopment)
        {
            var path = Path.Combine(AppContext.BaseDirectory, "assets", "service-account-key-super-app-prod.json");
            if (!isDevelopment)
            {
                path = Path.Combine(AppContext.BaseDirectory, "assets", "service-account-key-super-app-prod.json");
            }
            Environment.SetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS", path);
        }
        public static void SetEnvironmentVariables(bool isDevelopment)
        {
            var path = Path.Combine(AppContext.BaseDirectory, "service-account-key-super-app-prod.json");
            if (!isDevelopment)
            {
                path = Path.Combine(AppContext.BaseDirectory, "service-account-key-super-app-prod.json");
            }
            Environment.SetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS", path);
        }
        public static bool IsDeveloper()
        {
            var debugMode = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            return !string.IsNullOrEmpty(debugMode) && debugMode.ToLower().Equals("development");
            //return false;
        }

        public static string GetEnvironmentName()
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            return environmentName;
        }

        public static T GetAppSettingByKey<T>(string key)
        {
            try
            {
                var debugMode = IsDeveloper() ? "development" : "production";
                var builder = new ConfigurationBuilder()
                    .SetBasePath(AppContext.BaseDirectory)
                    .AddJsonFile("appsettings.json")
                    .AddJsonFile($"appsettings.{debugMode}.json", optional: true);
                var configuration = builder.Build();

                if (string.IsNullOrEmpty(configuration[key]))
                    return default;

                var value = configuration[key];

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (Exception ex)
            {
                return default;
            }
        }
        public static DateTime ConvertToDateTime(long timestamp)
        {
            var dateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            dateTime = dateTime.AddSeconds(timestamp);

            return dateTime;
        }

        public static double RoundUp(double number, int places)
        {
            var factor = RoundFactor(places);
            number *= factor;
            number = Math.Ceiling(number);
            number /= factor;
            return number;
        }

        public static double RoundDown(double number, int places)
        {
            var factor = RoundFactor(places);
            number *= factor;
            number = Math.Floor(number);
            number /= factor;
            return number;
        }

        internal static double RoundFactor(int places)
        {
            var factor = 1;

            if (places < 0)
            {
                places = -places;
                for (var i = 0; i < places; i++)
                    factor /= 10;
            }

            else
            {
                for (var i = 0; i < places; i++)
                    factor *= 10;
            }

            return factor;
        }

        public static string StripHtml(string source)
        {
            try
            {
                // Remove HTML Development formatting
                // Replace line breaks with space
                // because browsers inserts space
                var result = source.Replace("\r", " ");
                // Replace line breaks with space
                // because browsers inserts space
                result = result.Replace("\n", " ");
                // Remove step-formatting
                result = result.Replace("\t", string.Empty);
                // Remove repeating spaces because browsers ignore them
                result = Regex.Replace(result, @"( )+", " ", RegexOptions.None, TimeSpan.FromMilliseconds(3000));

                result = Regex.Replace(result,
                         @">( )*<", "><", RegexOptions.None, TimeSpan.FromMilliseconds(3000));

                // Remove the header (prepare first by clearing attributes)
                result = Regex.Replace(result,
                         @"<( )*head([^>])*>", "<head>",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"(<( )*(/)( )*head( )*>)", "</head>",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         "(<head>).*(</head>)", string.Empty,
                         RegexOptions.IgnoreCase);

                // remove all scripts (prepare first by clearing attributes)
                result = Regex.Replace(result,
                         @"<( )*script([^>])*>", "<script>",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"(<( )*(/)( )*script( )*>)", "</script>",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                //result = Regex.Replace(result,
                //         @"(<script>)([^(<script>\.</script>)])*(</script>)",
                //         string.Empty,
                //         RegexOptions.IgnoreCase);
                result = Regex.Replace(result,
                         @"(<script>).*(</script>)", string.Empty,
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));

                // remove all styles (prepare first by clearing attributes)
                result = Regex.Replace(result,
                         @"<( )*style([^>])*>", "<style>",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"(<( )*(/)( )*style( )*>)", "</style>",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         "(<style>).*(</style>)", string.Empty,
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));

                // insert tabs in spaces of <td> tags
                result = Regex.Replace(result,
                         @"<( )*tr([^>])*>( )*<( )*td([^>])*>", "\r\r",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"<( )*td([^>])*>", "\t",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));

                // insert line breaks in places of <BR> and <LI> tags
                result = Regex.Replace(result,
                         @"<( )*br( )*/>", "\r",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"<( )*li( )*>", "\r",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));

                // insert line paragraphs (double line breaks) in place
                // if <P>, <DIV> and <TR> tags
                result = Regex.Replace(result,
                         @"<( )*div([^>])*>", "\r\r",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"<( )*tr([^>])*>", "\r\r",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"<( )*p([^>])*>", "\r\r",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));

                // Remove remaining tags like <a>, links, images,
                // comments etc - anything that's enclosed inside < >
                result = Regex.Replace(result,
                         @"<[^>]*>", string.Empty,
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));

                // replace special characters:
                result = Regex.Replace(result,
                         @"&bull;", " * ",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"&lsaquo;", "<",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"&rsaquo;", ">",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"&trade;", "(tm)",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"&frasl;", "/",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"&lt;", "<",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"&gt;", ">",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"&copy;", "(c)",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         @"&reg;", "(r)",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                // Remove all others. More can be added, see
                // http://hotwired.lycos.com/webmonkey/reference/special_characters/
                result = Regex.Replace(result,
                         @"&(.{2,6});", string.Empty,
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));

                // for testing
                //Regex.Replace(result,
                //       this.txtRegex.Text,string.Empty,
                //       RegexOptions.IgnoreCase);

                // make line breaking consistent
                result = result.Replace("\n", "\r");

                result = Regex.Replace(result,
                         "^(\r)+", string.Empty,
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                // Remove extra line breaks and tabs:
                // replace over 2 breaks with 2 and over 4 tabs with 4.
                // Prepare first to remove any whitespaces in between
                // the escaped characters and remove redundant tabs in between line breaks
                result = Regex.Replace(result,
                         "(\r)( )+(\r)", "\r\r",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         "(\t)( )+(\t)", "\t\t",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         "(\t)( )+(\r)", "\t\r",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                result = Regex.Replace(result,
                         "(\r)( )+(\t)", "\r\t",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                // Remove redundant tabs
                result = Regex.Replace(result,
                         "(\r)(\t)+(\r)", "\r\r",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                // Remove multiple tabs following a line break with just one tab
                result = Regex.Replace(result,
                         "(\r)(\t)+", "\r\t",
                         RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
                // Initial replacement target string for line breaks
                var breaks = "\r\r\r";
                // Initial replacement target string for tabs
                var tabs = "\t\t\t\t\t";
                for (var index = 0; index < result.Length; index++)
                {
                    result = result.Replace(breaks, "\r\r");
                    result = result.Replace(tabs, "\t\t\t\t");
                    breaks = breaks + "\r";
                    tabs = tabs + "\t";
                }

                // That's it.
                return result;
            }
            catch
            {
                return source;
            }
        }
        public static string FormatPhoneNumber(string phoneNumber)
        {
            Regex regEx = new Regex(@"\D", RegexOptions.None, TimeSpan.FromMilliseconds(3000));
            if (string.IsNullOrEmpty(phoneNumber)) return phoneNumber;
            
            var result = regEx.Replace(phoneNumber, "");
            if (!result.StartsWith("0") && result.Length < 10)
            {
                result = "0" + result;
            }
            if (result.StartsWith("02") || result.StartsWith("03") || result.StartsWith("05")
                || result.StartsWith("07") || result.StartsWith("08") || result.StartsWith("09"))
            {
                result = "84" + result.Substring(1);
            }
            return result;
        }
        public static bool IsPhoneNumberVN(string number)
        {
            if (!number.StartsWith("02") && number.Length == 10)
                return Regex.Match(number, @"^(03[2|3|4|5|6|7|8|9]|"
                   + "05[2|5|6|8|9]|"
                    + "07[0|6|7|8|9]|"
                    + "08[1|2|3|4|5|6|7|8|9]|"
                    + "09[0|1|2|3|4|6|7|8])+([0-9]{7})$", RegexOptions.None, TimeSpan.FromMilliseconds(3000)).Success;
            else if ((number.StartsWith("024") || number.StartsWith("028")) && number.Length == 11)
                return Regex.Match(number, @"^(02|[4|8|])+([0-9]{8})$", RegexOptions.None, TimeSpan.FromMilliseconds(3000)).Success;
            else
                return Regex.Match(number, @"^(02|[03|04|05|06|07|08|09|10|11|12|13|14|15|16|18|19|20|21|22|25|26|27|28|29|"
                    + "|32|33|34|35|36|37|38|39|"
                    + "|51|52|54|55|56|57|58|59|60|61|62|63|"
                    + "|70|71|72|73|74|75|76|77|"
                    + "|90|91|92|93|96|97|99|])+([0-9]{7})$", RegexOptions.None, TimeSpan.FromMilliseconds(3000)).Success;
        }
        public static bool IsValidEmail(string email)
        {
            try
            {
                if (!string.IsNullOrEmpty(email) && email.Contains("@"))
                {
                    var addr = new System.Net.Mail.MailAddress(email);
                    return addr.Address == email;
                }
                else
                    return false;
            }
            catch
            {
                return false;
            }
        }
        public static bool isEmailValid(string inputEmail)
        {
            inputEmail = inputEmail ?? string.Empty;
            string strRegex = @"^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}" +
                  @"\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\" +
                  @".)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$";
            Regex re = new Regex(strRegex, RegexOptions.None, TimeSpan.FromMilliseconds(3000));
            if (re.IsMatch(inputEmail))
                return (true);
            else
                return (false);
        }

        public static bool VerifyEmail(string emailVerify)
        {
            using (WebClient webclient = new WebClient())
            {
                string url = "http://verify-email.org/";
                NameValueCollection formData = new NameValueCollection();
                formData["check"] = emailVerify;
                byte[] responseBytes = webclient.UploadValues(url, "POST", formData);
                string response = Encoding.ASCII.GetString(responseBytes);
                if (response.Contains("Result: Ok"))
                {
                    return true;
                }
                return false;
            }
        }
        public static string ToQueryString<T>(T obj)
        {
            var properties = from p in obj.GetType().GetRuntimeProperties()
                             where p.GetValue(obj, null) != null
                             select p.Name + "=" + WebUtility.UrlEncode(p.GetValue(obj, null).ToString());

            // queryString will be set to "Id=1&State=26&Prefix=f&Index=oo"                  
            return string.Join("&", properties.ToArray());
        }
        
        //public static DateTime StringToDate(string strDt, string strFormat = "d/M/yyyy H:m:s")
        //{
        //    return DateTime.ParseExact(strDt, strFormat, System.Globalization.CultureInfo.InvariantCulture);
        //}
        //public static string ConvertToVNDateTime(DateTime dateTime)
        //{
        //    return dateTime.ToString(Constants.SmsDateTimeFormat, CultureInfo.InvariantCulture);
        //}
        public static string TimeAgo(DateTime dt)
        {
            TimeSpan span = DateTime.Now - dt;
            if (span.Days > 365)
            {
                int years = (span.Days / 365);
                if (span.Days % 365 != 0)
                    years += 1;
                return String.Format("about {0} {1} ago",
                years, years == 1 ? "year" : "years");
            }
            if (span.Days > 30)
            {
                int months = (span.Days / 30);
                if (span.Days % 31 != 0)
                    months += 1;
                return String.Format("about {0} {1} ago",
                months, months == 1 ? "month" : "months");
            }
            if (span.Days > 0)
                return String.Format("about {0} {1} ago",
                span.Days, span.Days == 1 ? "day" : "days");
            if (span.Hours > 0)
                return String.Format("about {0} {1} ago",
                span.Hours, span.Hours == 1 ? "hour" : "hours");
            if (span.Minutes > 0)
                return String.Format("about {0} {1} ago",
                span.Minutes, span.Minutes == 1 ? "minute" : "minutes");
            if (span.Seconds > 5)
                return String.Format("about {0} seconds ago", span.Seconds);
            if (span.Seconds <= 5)
                return "just now";
            return string.Empty;
        }
        public static string UppercaseFirstEach(string s)
        {
            s = s.Trim();
            char[] a = s.ToLower().ToCharArray();
            for (int i = 0; i < a.Count(); i++)
            {
                a[i] = i == 0 || a[i - 1] == ' ' ? char.ToUpper(a[i]) : a[i];
            }
            return new string(a);
        }
        public static DataTable ToDataTable<T>(List<T> items)
        {
            DataTable dataTable = new DataTable(typeof(T).Name);

            if(items != null)
            {
                //Get all the properties
                PropertyInfo[] Props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
                foreach (PropertyInfo prop in Props)
                {
                    //Setting column names as Property names
                    dataTable.Columns.Add(prop.Name);
                }
                foreach (T item in items)
                {
                    var values = new object[Props.Length];
                    for (int i = 0; i < Props.Length; i++)
                    {
                        //inserting property values to datatable rows
                        values[i] = Props[i].GetValue(item, null);
                    }
                    dataTable.Rows.Add(values);
                }
            } 

            //put a breakpoint here and check datatable
            return dataTable;
        }

        //public static string MarkdownToHtml(string value)
        //{
        //    var result = Markdown.ToHtml(value);
        //    return result;
        //}
    }
   
}
