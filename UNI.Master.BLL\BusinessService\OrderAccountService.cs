﻿using UNI.Model;
using UNI.Master.Model.UniMaster;
using System;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.DAL.Interfaces;

namespace UNI.Master.BLL.BusinessService
{
    public class OrderAccountService : IOrderAccountService
    {
        private readonly IOrderAccountRepository _orderAccountRepository;
        public OrderAccountService(IOrderAccountRepository orderAccountRepository)
        {
            if (orderAccountRepository != null)
                _orderAccountRepository = orderAccountRepository;
        }
        public Task<CommonListPage> GetOrderAccountPage(FilterOrderAccount flt)
        {
            return _orderAccountRepository.GetOrderAccountPage(flt);
        }

        public Task<OrderAccountInfo> GetOrderAccountInfo(string userId, Guid? id)
        {
            return _orderAccountRepository.GetOrderAccountInfo(userId, id);
        }

        public Task<BaseValidate> SetOrderAccountInfo(string userId, OrderAccountInfo prod)
        {
            return _orderAccountRepository.SetOrderAccountInfo(userId, prod);
        }

        public Task<BaseValidate> DelOrderAccountInfo(string userId, Guid id)
        {
            return _orderAccountRepository.DelOrderAccountInfo(userId, id);
        }
    }
}
