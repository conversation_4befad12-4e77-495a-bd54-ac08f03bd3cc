using System.Net;
using System.Text;
using System.Text.Json;
using Moq;
using Moq.Protected;
using UNI.Master.HarborClient.Models.Artifacts;
using UNI.master.Test.Fixtures;
using UNI.Master.HarborClient.Models.Projects;
using UNI.Master.HarborClient.Models.Repositories;

namespace UNI.master.Test.IntegrationTests;

public class HarborClientTests : IClassFixture<HarborClientFixture>
{
    private readonly HarborClientFixture _fixture;
    /// <summary>
    /// 
    /// </summary>
    /// <param name="fixture"></param>
    public HarborClientTests(HarborClientFixture fixture)
    {
        _fixture = fixture;
    }

    #region ProjectService Tests

    [Fact]
    public async Task ListProjectsAsync_ReturnsProjects_WhenSuccessful()
    {
        // Arrange
        var expectedProjects = new List<ProjectModel>
        {
            new ProjectModel(1, "project1", "admin", DateTime.UtcNow),
            new ProjectModel(2, "project2", "user", DateTime.UtcNow)
        };

        SetupMockResponse(
            HttpMethod.Get,
            "/api/v2.0/projects?page=1&page_size=10",
            HttpStatusCode.OK,
            expectedProjects
        );

        // Act
        var result = await _fixture.HarborClient.Projects.ListProjectsAsync();

        // Assert
        Assert.Equal(2, result.Count());
        Assert.Contains(result, p => p.ProjectId == 1);
        Assert.Contains(result, p => p.Name == "project2");
    }

    [Fact]
    public async Task CreateProjectAsync_ReturnsCreatedProject_WhenValid()
    {
        // Arrange
        var newProject = new ProjectModel(
            0, // ID should be generated by server
            "new-project",
            "admin",
            DateTime.UtcNow
        );

        var expectedProject = newProject with { ProjectId = 1 };

        //SetupMockResponse(
        //    HttpMethod.Post,
        //    "/api/v2.0/projects",
        //    HttpStatusCode.Created,
        //    expectedProject,
        //    request => request.Content.ReadAsStringAsync().Result ==
        //        JsonSerializer.Serialize(newProject)
        //);

        // Act
        var result = await _fixture.HarborClient.Projects.CreateProjectAsync(newProject);

        // Assert
        Assert.Equal(1, result.ProjectId);
        Assert.Equal("new-project", result.Name);
    }

    [Fact]
    public async Task DeleteProjectAsync_Succeeds_WhenProjectExists()
    {
        // Arrange
        const int projectId = 123;
        SetupMockResponse(
            HttpMethod.Delete,
            $"/api/v2.0/projects/{projectId}",
            HttpStatusCode.NoContent
        );

        // Act & Assert
        await _fixture.HarborClient.Projects.DeleteProjectAsync(projectId);
    }

    [Fact]
    public async Task ListProjectsAsync_Throws_WhenUnauthorized()
    {
        // Arrange
        SetupMockResponse(
            HttpMethod.Get,
            "/api/v2.0/projects",
            HttpStatusCode.Unauthorized
        );

        // Act & Assert
        await Assert.ThrowsAsync<HttpRequestException>(
            () => _fixture.HarborClient.Projects.ListProjectsAsync()
        );
    }

    #endregion

    #region RepositoryService Tests

    [Fact]
    public async Task ListRepositoriesAsync_ReturnsRepositories_WhenSuccessful()
    {
        // Arrange
        const string projectName = "unimaster";
        var expectedRepos = new List<RepositoryModel>
        {
            new RepositoryModel(projectName, 2, "Description 1", 3),
        };

        //SetupMockResponse(
        //    HttpMethod.Get,
        //    $"/api/v2.0/projects/{projectId}/repositories",
        //    HttpStatusCode.OK,
        //    expectedRepos
        //);

        // Act
        var result = await _fixture.HarborClient.Repositories.ListRepositoriesAsync(projectName);

        // Assert
        Assert.True(result.Any());
    }

    [Fact]
    public async Task DeleteRepositoryAsync_Succeeds_WithEncodedName()
    {
        // Arrange
        const string repoName = "library/nginx@sha256:abc123";
        var encodedName = Uri.EscapeDataString(repoName);

        //SetupMockResponse(
        //    HttpMethod.Delete,
        //    $"/api/v2.0/repositories/{encodedName}",
        //    HttpStatusCode.NoContent
        //);

        // Act & Assert
        await _fixture.HarborClient.Repositories.DeleteRepositoryAsync(repoName);
    }

    #endregion

    #region Common Tests

    [Fact]
    public async Task AllRequests_IncludeAuthenticationHeader()
    {
        // Arrange
        var expectedAuth = "Basic " + Convert.ToBase64String(
            Encoding.UTF8.GetBytes("test-user:test-password"));

        //SetupMockResponse(
        //    HttpMethod.Get,
        //    "/api/v2.0/projects",
        //    HttpStatusCode.OK,
        //    new List<ProjectModel>(),
        //    request => request.Headers.Authorization?.ToString() == expectedAuth
        //);

        // Act
        await _fixture.HarborClient.Projects.ListProjectsAsync();
    }

    [Fact]
    public async Task ListProjectsAsync_HandlesPagination()
    {
        // Arrange
        const int page = 2;
        const int pageSize = 5;

        //SetupMockResponse(
        //    HttpMethod.Get,
        //    $"/api/v2.0/projects?page={page}&page_size={pageSize}",
        //    HttpStatusCode.OK,
        //    new List<ProjectModel>()
        //);

        // Act
        await _fixture.HarborClient.Projects.ListProjectsAsync(page, pageSize);
    }

    [Fact]
    public async Task ListArtifactsAsync_ReturnsValidArtifacts()
    {
        const string projectName = "unimaster";
        const string repositoryName = "unibzzcloudapi";

        // Arrange
        var expectedArtifacts = new List<ArtifactModel>
        {
            new ArtifactModel("sha256:36cad90eec536c29487aff74cf04f0ab6799c64cac3d10f9d93378df6981a75f", new List<TagModel>(){new TagModel(){Name = "v1.0.0" } }, "docker pull...",
                DateTime.Now, "application/vnd.docker...", 1024, "")
        };

        //SetupMockResponse(
        //    HttpMethod.Get,
        //    "/api/v2.0/projects/my-project/repositories/my-repo/artifacts?page=1&page_size=10",
        //    HttpStatusCode.OK,
        //    expectedArtifacts
        //);

        // Act
        var result = await _fixture.HarborClient.Artifacts
            .ListArtifactsAsync(projectName, repositoryName);

        // Assert
        Assert.NotEmpty(result);
    }
    #endregion

    private void SetupMockResponse(
        HttpMethod method,
        string path,
        HttpStatusCode statusCode,
        object content = null,
        Func<HttpRequestMessage, bool> requestValidator = null)
    {
        _fixture.MockHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(req =>
                    req.Method == method &&
                    req.RequestUri.PathAndQuery == path &&
                    (requestValidator == null || requestValidator(req))),
                ItExpr.IsAny<CancellationToken>()
            )
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = statusCode,
                Content = content != null
                    ? new StringContent(
                        JsonSerializer.Serialize(content),
                        Encoding.UTF8,
                        "application/json")
                    : null
            })
            .Verifiable();
    }
}