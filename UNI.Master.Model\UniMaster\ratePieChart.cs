﻿using System;
using System.Collections.Generic;

namespace UNI.Master.Model.UniMaster
{
    public class ratePieChart
    {
        public int total { get; set; }
        public int totalFiltered { get; set; }
        public List<ratePieChartInfo> rate { get; set; }
    }
    public class ratePieChartInfo
    {
        public string rate_name { get; set; }
        public int num_rate { get; set; }
    }
    public class rateUnsatifiedChart
    {
        public int total { get; set; }
        public int totalFiltered { get; set; }
        public List<rateUnsatifiedChartInfo> Unsatified { get; set; }
    }
    public class rateUnsatifiedChartInfo
    {
        public string rate_name { get; set; }
        public int num_rate { get; set; }
    }
    public class ratingRateOfCustomer
    {
        public List<ratingRateOfCustomerInfo> ratingRate { get; set; }
        public bool valid { get; set; }
        public string messages { get; set; }
        public int? total { get; set; }
        public int? totalFiltered { get; set; }
    }
    public class ratingRateOfCustomerInfo
    {
        public Guid? rate_Id { get; set; }
        public string rate_name { get; set; }
        public List<ratingRateOfCustomerProperties> child { get; set; }
    }
    public class ratingRateOfCustomerProperties
    {
        public Guid? area_id { get; set; }
        public Guid? rate_Id { get; set; }
        public string area_name { get; set; }
        public string num_rate { get; set; }
    }
    public class rateUnsatifiedList
    {
        public int total { get; set; }
        public int totalFiltered { get; set; }
        public List<rateUnsatifiedListInfo> Unsatified { get; set; }
    }
    public class rateUnsatifiedListInfo
    {
        public string rate_dec { get; set; }
        public int num_rate { get; set; }
    }
}
