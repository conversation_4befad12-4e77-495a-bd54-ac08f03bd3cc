﻿using Dapper;
using Microsoft.Extensions.Configuration;
using UNI.Model;
using UNI.Master.Model.UniMaster;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using UNI.Master.DAL.Interfaces;
using FilterOrder = UNI.Master.Model.UniMaster.FilterOrder;

namespace UNI.Master.DAL.Repositories
{
    /// <summary>
    /// WebRoleRepository 
    /// </summary>
    /// Author: 
    /// CreatedDate: 16/11/2016 2:07 PM
    /// <seealso cref="OrderRepository" />
    public class OrderRepository : IOrderRepository
    {
        private readonly string _connectionString;

        public OrderRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("dbUniMasterConnection");
        }
               
        public async Task<CommonListPage> GetOrderPage(FilterOrder flt)
        {
            const string storedProcedure = "sp_order_get";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                param.Add("@UserId", flt.userId);
                param.Add("@filter", flt.filter);
                param.Add("@cust_id", flt.cust_id);

                param.Add("@Offset", flt.offSet);
                param.Add("@PageSize", flt.pageSize);
                param.Add("@Total", 0, DbType.Int64, ParameterDirection.InputOutput);
                param.Add("@TotalFiltered", 0, DbType.Int64, ParameterDirection.InputOutput);
                    
                var result = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
                var data = new CommonListPage();
                if (flt.offSet == null || flt.offSet == 0)
                {
                    data.gridflexs = result.Read<viewGridFlex>().ToList();
                }
                var deplist = result.Read<object>().ToList();
                data.dataList = new ResponseList<List<object>>(deplist, param.Get<long>("@Total"), param.Get<long>("@TotalFiltered"));
                return data;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<OrderInfo> GetOrderInfo(string userId, Guid? id)
        {
            const string storedProcedure = "sp_order_fields";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                param.Add("@userId", userId);
                param.Add("@id", id);
                var result = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
                var data = result.ReadFirstOrDefault<OrderInfo>();
                if (data != null)
                {
                    data.group_fields = result.Read<viewGroup>().ToList();
                    if (data.group_fields != null && data.group_fields.Count > 0)
                    {
                        var flds = result.Read<viewField>().ToList();
                        if (flds.Count > 0)
                        {
                            foreach (var gr in data.group_fields)
                            {
                                gr.fields = flds.Where(f => f.group_cd == gr.group_cd).ToList();
                            }
                        }
                    }

                }
                return data;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<BaseValidate> SetOrderInfo(string userId, OrderInfo role)
        {
            const string storedProcedure = "sp_order_set";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                param.Add("@userId", userId);
                param.Add("@id", role.Id);
                param.Add("@cust_id", role.GetValueByFieldName("cust_id"));
                param.Add("@ord_name", role.GetValueByFieldName("ord_name"));
                param.Add("@ord_no", role.GetValueByFieldName("ord_no"));
                param.Add("@ord_status", role.GetValueByFieldName("ord_status"));
                param.Add("@ord_status_pay", role.GetValueByFieldName("ord_status_pay"));
                param.Add("@voucher_no", role.GetValueByFieldName("voucher_no"));
                var result = await connection.QueryFirstAsync<BaseValidate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                return result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<BaseValidate> DelOrderInfo(string userId, Guid id)
        {
            const string storedProcedure = "sp_order_del";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                param.Add("@userId", userId);
                param.Add("@id", id);
                var result = await connection.QueryFirstAsync<BaseValidate>(storedProcedure, param, commandType: CommandType.StoredProcedure);
                return result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public async Task<List<CommonValue>> GetOrderList(string customer_id, string filter)
        {
            const string storedProcedure = "sp_order_list_get";
            try
            {
                await using SqlConnection connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                var param = new DynamicParameters();
                param.Add("@customer_id", customer_id);
                param.Add("@filter", filter);
                var result = connection.Query<CommonValue>(storedProcedure, param, commandType: CommandType.StoredProcedure).ToList();
                return result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

    }
}
