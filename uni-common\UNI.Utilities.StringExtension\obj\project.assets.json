{"version": 3, "targets": {".NETStandard,Version=v2.1": {"UNI.Utilities.Reflection/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.0", "compile": {"bin/placeholder/UNI.Utilities.Reflection.dll": {}}, "runtime": {"bin/placeholder/UNI.Utilities.Reflection.dll": {}}}}}, "libraries": {"UNI.Utilities.Reflection/1.0.0": {"type": "project", "path": "../UNI.Utilities.Reflection/UNI.Utilities.Reflection.csproj", "msbuildProject": "../UNI.Utilities.Reflection/UNI.Utilities.Reflection.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["UNI.Utilities.Reflection >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utilities.StringExtension\\UNI.Utilities.StringExtension.csproj", "projectName": "UNI.Utilities.StringExtension", "projectPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utilities.StringExtension\\UNI.Utilities.StringExtension.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utilities.StringExtension\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utilities.Reflection\\UNI.Utilities.Reflection.csproj": {"projectPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utilities.Reflection\\UNI.Utilities.Reflection.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}