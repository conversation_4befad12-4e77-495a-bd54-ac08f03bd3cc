﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using UNI.Master.BLL.Interfaces.Database;
using UNI.Master.DAL.Interfaces;
using UNI.Master.Model.Database;

namespace UNI.Master.BLL.BusinessService.Database
{
    public class DatabaseService : IDatabaseService
    {
        private readonly IDatabaseRepository _databaseRepository;
        private readonly IMapper _mapper;
        public DatabaseService(IDatabaseRepository databaseRepository, IMapper mapper)
        {
            _databaseRepository = databaseRepository;
            _mapper = mapper;
        }

        public async Task<IEnumerable<DatabaseInstanceDto>> GetInstancesAsync()
        {
            throw new NotImplementedException();
            //var instances = await _dbContext.Dbinstances.ToListAsync();
            //return _mapper.Map<IEnumerable<DatabaseInstanceDto>>(instances);
        }
    }
}
