﻿using UNI.Master.Model.UniMaster;
using UNI.Model;
using System;
using System.Threading.Tasks;

namespace UNI.Master.DAL.Interfaces
{
    public interface IOrderAccountRepository
    {
        Task<CommonListPage> GetOrderAccountPage(FilterOrderAccount flt);
        Task<OrderAccountInfo> GetOrderAccountInfo(string userId, Guid? id);
        Task<BaseValidate> SetOrderAccountInfo(string userId, OrderAccountInfo prod);
        Task<BaseValidate> DelOrderAccountInfo(string userId, Guid id);
    }
}
