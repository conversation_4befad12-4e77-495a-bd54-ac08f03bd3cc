﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;
using System.IO;
using Elastic.Apm.SerilogEnricher;
using Elastic.Channels;
using Elastic.Ingest.Elasticsearch;
using Elastic.Ingest.Elasticsearch.DataStreams;
using Elastic.Serilog.Sinks;
using Elastic.Transport;
using Serilog.Events;

namespace UNI.Master.API
{
    /// <summary>
    /// Program
    /// </summary>
    public class Program
    {
        const String LogTemplate = "[{ElasticApmTraceId} {ElasticApmTransactionId} {ElasticApmSpanId} {Message:lj} {NewLine}{Exception}";
        /// <summary>
        /// Main
        /// </summary>
        /// <param name="args"></param>
        public static void Main(string[] args)
        {
            var config = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .AddJsonFile($"appsettings.{UNI.Common.Utils.GetEnvironmentName()}.json", optional: true, reloadOnChange: true)
                .Build();

            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(config)
                .CreateLogger();
            try
            {
                Log.Information("Application Starting.");
                 CreateHostBuilder(args).Build().Run();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "The Application failed to start.");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        /// <summary>
        /// Create Host Builder
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public static IHostBuilder CreateHostBuilder(string[] args) =>
         Host.CreateDefaultBuilder(args)
            .UseSerilog((context, services, configuration) =>
            {
                var elsUrl = context.Configuration["ElasticSearch:Url"] ?? "";
                var elsUserName = context.Configuration["ElasticSearch:UserName"] ?? "";
                var elsPassword = context.Configuration["ElasticSearch:Password"] ?? "";

                configuration
                    .ReadFrom.Configuration(context.Configuration)
                    .ReadFrom.Services(services)
                    .Enrich.WithElasticApmCorrelationInfo()
                    // .Enrich.WithEcsHttpContext(httpAccessor)
                    .WriteTo.Console(LogEventLevel.Debug, outputTemplate: LogTemplate);
                // .WriteTo.File("logs/serilog.log", LogEventLevel.Debug,
                //     rollingInterval: RollingInterval.Day,
                //     outputTemplate: LogTemplate);

                if (!string.IsNullOrEmpty(elsUrl) && !string.IsNullOrEmpty(elsUserName) && !string.IsNullOrEmpty(elsPassword))
                {
                    configuration.WriteTo.Elasticsearch(new[] { new Uri(elsUrl), }, opts =>
                    {
                        opts.DataStream = new DataStreamName("logs", context.Configuration["ElasticApm:ServiceName"], context.Configuration["ElasticApm:Environment"]);
                        opts.BootstrapMethod = BootstrapMethod.None;
                        opts.ConfigureChannel = channelOpts =>
                        {
                            channelOpts.BufferOptions = new BufferOptions
                            {
                                ExportMaxConcurrency = 10
                            };
                        };
                    }, http =>
                    {
                        http.Authentication(new BasicAuthentication(elsUserName, elsPassword));
                        //ignore certificate validation
                        http.ServerCertificateValidationCallback(
                            (sender, certificate, chain, sslPolicyErrors) => true);
                    });
                }
            })
             .UseContentRoot(Directory.GetCurrentDirectory())
             .ConfigureWebHostDefaults(webBuilder =>
              {
                  webBuilder.UseStartup<Startup>();
              });
    }
}
