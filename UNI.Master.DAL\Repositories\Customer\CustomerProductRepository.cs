﻿using System;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces.Customer;
using UNI.Master.Model.Common;
using UNI.Master.Model.Customers;
using UNI.Model;

namespace UNI.Master.DAL.Repositories.Customer
{
    public class CustomerProductRepository : UniBaseRepository, ICustomerProductRepository
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="common"></param>
        public CustomerProductRepository(IUniCommonBaseRepository common) : base(common)
        {
        }

        public Task<CommonListPage> GetPageAsync(CustomerProductFilter query)
        {
            return GetPageAsync("sp_customer_products_page", query, new {query.CustomerId});
        }

        public Task<MasterCommonInfo> GetInfoAsync(Guid? id, Guid? customerId)
        {
            return GetFieldsAsync<MasterCommonInfo>("sp_customer_products_fields", new { id, customerId });
        }

        public Task<BaseValidate> SetInfoAsync(MasterCommonInfo prod)
        {
            return SetInfoAsync("sp_customer_products_set", prod, new { prod.id });
        }

        public Task<BaseValidate> DeleteAsync(Guid? id)
        {
            return DeleteAsync("sp_customer_products_del", new { id });
        }

    }
}
