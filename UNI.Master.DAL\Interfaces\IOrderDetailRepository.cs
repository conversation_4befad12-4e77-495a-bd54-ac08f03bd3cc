﻿using UNI.Master.Model.UniMaster;
using UNI.Model;
using System;
using System.Threading.Tasks;

namespace UNI.Master.DAL.Interfaces
{
    public interface IOrderDetailRepository
    {
        Task<CommonListPage> GetOrderDetailPage(FilterOrderDetail flt);
        Task<OrderDetailInfo> GetOrderDetailInfo(string userId, Guid? id);
        Task<BaseValidate> SetOrderDetailInfo(string userId, OrderDetailInfo prod);
        Task<BaseValidate> DelOrderDetailInfo(string userId, Guid id);
    }
}
