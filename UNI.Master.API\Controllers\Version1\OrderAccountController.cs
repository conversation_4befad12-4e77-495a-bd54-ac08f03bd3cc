﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.Model.UniMaster;
using UNI.Model;
using UNI.Model.Api;

namespace UNI.Master.API.Controllers.Version1
{
    /// <summary>
    /// Web Role Controller
    /// </summary>
    /// Author: duongpx
    /// CreatedDate: 07/04/2020 9:31 AM
    /// <seealso cref="OrderAccountController" />
    [Route("api/v1/orderAccount/[action]")]
    [Authorize]
    public class OrderAccountController : UniController
    {
        private readonly IOrderAccountService _orderAccountService;
        private readonly ISysManageService _systemService;

        /// <summary>
        /// Web Controller
        /// </summary>
        /// <param name="orderAccountService"></param>
        /// <param name="appSettings"></param>
        /// <param name="logger"></param>
        public OrderAccountController(
            IOrderAccountService orderAccountService,
             ISysManageService systemService,
            IOptions<AppSettings> appSettings,
            ILoggerFactory logger) : base(appSettings, logger)
        {
            _orderAccountService = orderAccountService;
        }

        /// <summary>
        /// GetOrderPage
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="pro_id"></param>
        /// <param name="gridWith"></param>
        /// <param name="offSet"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetOrderAccountPage(
            [FromQuery] string filter,
            [FromQuery] Guid? pro_id,
            [FromQuery] int gridWith,
            [FromQuery] int offSet,
            [FromQuery] int pageSize)
        {
            var flt = new FilterOrderAccount(this.ClientId, this.UserId, offSet, pageSize, filter, gridWith, pro_id);
            var result = await _orderAccountService.GetOrderAccountPage(flt);
            return GetResponse<CommonListPage>(ApiResult.Success, result);
        }
        /// <summary>
        /// GetProductInfo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<OrderAccountInfo>> GetOrderAccountInfo([FromQuery] Guid? id)
        {
            var result = await _orderAccountService.GetOrderAccountInfo(this.UserId, id);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// SetProductInfo
        /// </summary>
        /// <param name="prod"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<string>> SetOrderAccountInfo([FromBody] OrderAccountInfo prod)
        {
            var result = await _orderAccountService.SetOrderAccountInfo(this.UserId, prod);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
        /// <summary>
        /// DelProductInfo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<BaseResponse<string>> DelOrderAccountInfo([FromQuery] Guid id)
        {
            var result = await _orderAccountService.DelOrderAccountInfo(this.UserId, id);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
    }
}
