using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces;
using UNI.Master.EntityFrameworkCore.Entities;
using UNI.Master.Model.Database;
using UNI.Model;

namespace UNI.Master.DAL.Repositories
{
    public class DatabaseRepository : UniBaseRepository, IDatabaseRepository
    {
        public DatabaseRepository(IUniCommonBaseRepository common) : base(common)
        {
        }

        public Task<CommonListPage> GetPageAsync(DatabaseInstanceFilter filter)
        {
            return base.GetPageAsync("sp_database_instance_page", filter);
        }

        public Task<DatabaseInstanceDto> GetInfoAsync(Guid? id)
        {
            return base.GetFieldsAsync<DatabaseInstanceDto>("sp_database_instance_fields", new { id });
        }

        public Task<BaseValidate> SetInfoAsync(DatabaseInstanceDto info)
        {
            return base.SetInfoAsync("sp_database_instance_set", info, new { info.Id });
        }

        public Task<BaseValidate> DeleteAsync(Guid? id)
        {
            return base.DeleteAsync("sp_database_instance_delete", new { id });
        }

        public Task<IEnumerable<Dbinstance>> GetInstancesAsync()
        {
            return base.GetAsync<Dbinstance>("sp_database_instance_list", null);
        }
    }
}
