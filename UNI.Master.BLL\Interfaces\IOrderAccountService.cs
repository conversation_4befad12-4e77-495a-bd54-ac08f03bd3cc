﻿using System;
using System.Threading.Tasks;
using UNI.Master.Model.UniMaster;
using UNI.Model;

namespace UNI.Master.BLL.Interfaces
{
    public interface IOrderAccountService
    {
        Task<CommonListPage> GetOrderAccountPage(FilterOrderAccount flt);
        Task<OrderAccountInfo> GetOrderAccountInfo(string userId, Guid? id);
        Task<BaseValidate> SetOrderAccountInfo(string userId, OrderAccountInfo prod);
        Task<BaseValidate> DelOrderAccountInfo(string userId, Guid id);
    }
}
