﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>netstandard2.0</TargetFramework>
		<Nullable>enable</Nullable>
		<LangVersion>default</LangVersion>
	</PropertyGroup>

	<ItemGroup>
	  <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
	  <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
	  <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
	  <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\UNI.Utilities.HttpClientExtension\UNI.Utilities.HttpClientExtension.csproj" />
	  <ProjectReference Include="..\UNI.Utilities.JsonExtension\UNI.Utilities.JsonExtension.csproj" />
	</ItemGroup>

</Project>
