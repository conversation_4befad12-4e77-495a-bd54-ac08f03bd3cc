﻿using UNI.Model.Bank.KLBank;

namespace UNI.Model
{
    public class AppSettings
    {
        public BaseUrls BaseUrls { get; set; }
        public BaseUrl Server { get; set; }
        public Client Client { get; set; }
        public string ProjectId { get; set; }
        public string BrandName { get; set; }
        public string SendName { get; set; }
        public string AdminSafeList { get; set; }
        public bool AnalyticEnabled { get; set; }
        //public int MinuteExpired { get; set; }
        //public bool InitData { get; set; }
        //public string Language { get; set; }
        public NotifySetting Notify { get; set; }
        //public kApiSetting KSFHome { get; set; }
        //public kApiSetting KSFBond { get; set; }
        //public kApiSetting Kss { get; set; }
        //public kApiSetting Umee { get; set; }
        public ksbSettings Ksbank { get; set; }
        public klbSettings Klbank { get; set; }
        public string AppUrl { get; set; }
    }
    public class BaseUrls
    {
        public string Api { get; set; }
        public string Auth { get; set; }
        public string Web { get; set; }
        //public string SmsApi { get; set; }
        public string CoreApi { get; set; }
        public string Storage { get; set; }
        public string FrontendUrl { get; set; }
    }
    public class kApiSetting
    {
        public string xApiIpAdress { get; set; }
        public string XApiKey { get; set; }
        public string Host { get; set; }
    }
    public class CrmBankSetting : kApiSetting
    {
    }
    public class Client
    {
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
    }
    public class NotifySetting
    {
        public string ExternalKey { get; set; }
        public string SourceKey { get; set; }
    }

    public class Contract
    {
        public string Name { get; set; }
        public string DownloadFile { get; set; }
        public string TemplatePath { get; set; }
    }

    public class ksbSettings
    {
        public string StockUrl { get; set; }
        public string ProdUrl { get; set; }
        public string payEnvironment { get; set; }
        //public string ProdUrl { get; set; }
        //public string DevUrl { get; set; }
        public string StagUrl { get; set; }
        public string Version { get; set; }
        public string ChecksumKey { get; set; }
        public string AccessCode { get; set; }
        public string companyId { get; set; }
        public string ksbPayUri { get; set; }
        public string intPayUri { get; set; }
        public string accFetchUri { get; set; }
        public string getTransactionUri { get; set; }
        public string Locale { get; set; }
        public string CurrencyCode { get; set; }
        public string AdminSafeList { get; set; }
        public ksbXAPIKey XAPIKey { get; set; }
        //public ksbAuthenticationInfo AuthenticationInfo { get; set; }
    }
    public class ksbXAPIKey
    {
        public string Bond { get; set; }
        public string Cod { get; set; }
        public string Invest { get; set; }
        public string Residence { get; set; }
    }
    public class BaseUrl
    {
        public string baseUrl { get; set; }
    }
    public class ClientBaseApi
    {
        public string BaseUrl { get; set; }
        public string ApiKey { get; set; }
    }
}
