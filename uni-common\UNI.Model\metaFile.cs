﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;

namespace UNI.Model
{
    [AttributeUsage(AttributeTargets.Property, Inherited = true, AllowMultiple = false)]
    public class MapToFullUrlAttribute : Attribute
    {
        public string BaseUrl { get; }

        public MapToFullUrlAttribute(string baseUrl)
        {
            BaseUrl = baseUrl;
        }

        public MapToFullUrlAttribute()
        {
        }
    }

    public static class ObjectUtils
    {
        public static void MapRelativePathToAbsolutePath(this object obj, string endpoint = null)
        {
            var type = obj.GetType();
            foreach (var property in type.GetProperties())
            {
                var attribute =
                    (MapToFullUrlAttribute)Attribute.GetCustomAttribute(property, typeof(MapToFullUrlAttribute));
                if (attribute == null) continue;
                var relativeUrl = (string)property.GetValue(obj);
                //check if relativeUrl is already a full url then ignore
                if (Uri.TryCreate(relativeUrl, UriKind.Absolute, out _)) continue;
                
                var baseUrl = endpoint ?? attribute.BaseUrl;
                var fullUrl = $"{baseUrl}{relativeUrl}";
                property.SetValue(obj, fullUrl);
            }
        }
    }
    #region Storage
    public class QrResponseBase<T>
    {
        public string action { get; set; }
        public T payload { get; set; }
    }
    public class UploadResponse
    {
        public long Size { get; set; }
        public string ObjectName { get; set; }
        public string Bucket { get; set; }

        public string FilePath { get; set; }

        public string FileName { get; set; }
        public string ContentType { get; set; }

        public string Url { get; set; }

        public int UrlExpiration { get; set; }
    }
    public enum ImageSizeEnum
    {
        Sm, Md, La, Xl
    }

    public class UploadRequest
    {
        public string Name { get; set; }
        public string Path { get; set; }
    }
    public class StorageConfig
    {
        public string Endpoint { get; set; }

        public string ProxyEndpoint { get; set; }
        public string AccessKey { get; set; }
        public string SecretKey { get; set; }
        public string BucketName { get; set; }
        public string PrefixFolder { get; set; }

        public string Region { get; set; }
        public bool UseSsl { get; set; }
    }
    /// <summary>
    /// Dữ liệu trả về sau khi upload file
    /// </summary>
    /// <code>
    /// {
    /// "size": 52,
    /// "objectName": "uni-hrm/d9f5cb69-0897-4d7f-8b98-8f3df1153d0d_CT01_Thng_t_66.doc",
    /// "bucket": "sunshine-super-app.appspot.com",
    /// "filePath": "gs://sunshine-super-app.appspot.com/uni-hrm/d9f5cb69-0897-4d7f-8b98-8f3df1153d0d_CT01_Thng_t_66.doc",
    /// "fileName": "CT01_Thông_tư_66.doc",
    /// "contentType": "application/msword",
    /// "url": "https://storage.googleapis.com/download/storage/v1/b/sunshine-super-app.appspot.com/o/",
    /// "urlExpiration": 0
    /// }
    /// </code>
    public class FileStorageInfo
    {
        public string ObjectName { get; set; }
        public long Size { get; set; }
        public DateTime LastModified { get; set; }
        public string ETag { get; set; }
        public string ContentType { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public string Url { get; set; }
        public Guid? groupFileId { get; set; }
        public Guid? Oid { get; set; }
        public string source_type { get; set; }
        public string file_url { get; set; }
    }
    public class MediaFile
    {
        public Guid? Oid { get; set; }
        public Guid? parentOid { get; set; }
        public string source_type { get; set; }
        public string group_fields { get; set; }
        public IFormFile formFile { get; set; }
    }
    #endregion Storage

}