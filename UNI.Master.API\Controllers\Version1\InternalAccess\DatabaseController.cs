using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using UNI.Master.BLL.Interfaces.Database;
using UNI.Master.Model.Database;
using UNI.Model;
using UNI.Model.Api;

namespace UNI.Master.API.Controllers.Version1.InternalAccess
{

    /// <summary>
    /// Database Instance Management Controller
    /// </summary>
    [Route("api/v1/[controller]")]
    [Authorize]
    public class DatabaseController : UniController
    {
        private readonly IDatabaseService _databaseService;

        /// <inheritdoc />
        public DatabaseController(ILoggerFactory logger, IDatabaseService databaseService) : base(logger)
        {
            _databaseService = databaseService;
        }

        /// <summary>
        /// Get paginated list of database instances
        /// </summary>
        /// <param name="filter">Filter parameters</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(BaseResponse<CommonListPage>))]
        public async Task<IActionResult> GetPage([FromQuery] DatabaseInstanceFilter filter)
        {
            var result = await _databaseService.GetPageAsync(filter);
            var response = GetResponse(ApiResult.Success, result);
            return Ok(response);
        }

        /// <summary>
        /// Get all database instances (simple list)
        /// </summary>
        /// <returns></returns>
        [HttpGet("instances")]
        [ProducesDefaultResponseType(typeof(BaseResponse<IEnumerable<DatabaseInstanceDto>>))]
        public async Task<IActionResult> GetInstances()
        {
            var result = await _databaseService.GetInstancesAsync();
            var response = GetResponse(ApiResult.Success, result);
            return Ok(response);
        }

        /// <summary>
        /// Get database instance by ID
        /// </summary>
        /// <param name="id">Database instance ID</param>
        /// <returns></returns>
        [HttpGet("{id}")]
        [ProducesDefaultResponseType(typeof(BaseResponse<DatabaseInstanceDto>))]
        public async Task<IActionResult> GetInfo([FromRoute, Required] Guid id)
        {
            var result = await _databaseService.GetInfoAsync(id);
            if (result == null)
            {
                var notFoundResponse = GetResponse<DatabaseInstanceDto>(ApiResult.Error, null, "Database instance not found");
                return NotFound(notFoundResponse);
            }

            var response = GetResponse(ApiResult.Success, result);
            return Ok(response);
        }

        /// <summary>
        /// Create new database instance
        /// </summary>
        /// <param name="createDto">Database instance creation data</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(BaseResponse<string>))]
        public async Task<IActionResult> Create([FromBody] DatabaseInstanceCreateDto createDto)
        {
            if (!ModelState.IsValid)
            {
                var errorResponse = GetResponse<string>(ApiResult.Error, null, Errors);
                return BadRequest(errorResponse);
            }

            var result = await _databaseService.CreateAsync(createDto);
            var response = GetResponse(result.valid ? ApiResult.Success : ApiResult.Error, string.Empty, result.messages);
            return Ok(response);
        }

        /// <summary>
        /// Update existing database instance
        /// </summary>
        /// <param name="updateDto">Database instance update data</param>
        /// <returns></returns>
        [HttpPut]
        [ProducesDefaultResponseType(typeof(BaseResponse<string>))]
        public async Task<IActionResult> Update([FromBody] DatabaseInstanceUpdateDto updateDto)
        {
            if (!ModelState.IsValid)
            {
                var errorResponse = GetResponse<string>(ApiResult.Error, null, Errors);
                return BadRequest(errorResponse);
            }

            var result = await _databaseService.UpdateAsync(updateDto);
            var response = GetResponse(result.valid ? ApiResult.Success : ApiResult.Error, string.Empty, result.messages);
            return Ok(response);
        }

        /// <summary>
        /// Delete database instance
        /// </summary>
        /// <param name="id">Database instance ID</param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [ProducesDefaultResponseType(typeof(BaseResponse<string>))]
        public async Task<IActionResult> Delete([FromRoute, Required] Guid id)
        {
            var result = await _databaseService.DeleteAsync(id);
            var response = GetResponse(result.valid ? ApiResult.Success : ApiResult.Error, string.Empty, result.messages);
            return Ok(response);
        }
    }
}

