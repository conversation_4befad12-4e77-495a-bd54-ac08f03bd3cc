﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.Model.UniMaster;
using UNI.Model;
using UNI.Model.Api;

namespace UNI.Master.API.Controllers.Version1
{
    /// <summary>
    /// Web Role Controller
    /// </summary>
    /// Author: duongpx
    /// CreatedDate: 07/04/2020 9:31 AM
    /// <seealso cref="OrderDetailController" />Order
    [Route("api/v1/orderDetail/[action]")]
    [Authorize]
    public class OrderDetailController : UniController
    {
        private readonly IOrderDetailService _orderDetailService;
        private readonly ISysManageService _systemService;

        /// <summary>
        /// Web Controller
        /// </summary>
        /// <param name="orderDetailService"></param>
        /// <param name="appSettings"></param>
        /// <param name="logger"></param>
        public OrderDetailController(
            IOrderDetailService orderDetailService,
             ISysManageService systemService,
            IOptions<AppSettings> appSettings,
            ILoggerFactory logger) : base(appSettings, logger)
        {
            _orderDetailService = orderDetailService;
        }

        /// <summary>
        /// GetOrderDetailPage
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="gridWith"></param>
        /// <param name="offSet"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetOrderDetailPage(
            [FromQuery] string filter,
            [FromQuery] Guid? ord_id,
            [FromQuery] int gridWith,
            [FromQuery] int offSet,
            [FromQuery] int pageSize)
        {
            var flt = new FilterOrderDetail(this.ClientId, this.UserId, offSet, pageSize, filter, gridWith, ord_id);
            var result = await _orderDetailService.GetOrderDetailPage(flt);
            return GetResponse<CommonListPage>(ApiResult.Success, result);
        }
        /// <summary>
        /// GetOrderDetailInfo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<OrderDetailInfo>> GetOrderDetailInfo([FromQuery] Guid? id)
        {
            var result = await _orderDetailService.GetOrderDetailInfo(this.UserId, id);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// SetOrderDetailInfo
        /// </summary>
        /// <param name="prod"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<string>> SetOrderDetailInfo([FromBody] OrderDetailInfo prod)
        {
            var result = await _orderDetailService.SetOrderDetailInfo(this.UserId, prod);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
        /// <summary>
        /// DelOrderDetailInfo
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<BaseResponse<string>> DelOrderDetailInfo([FromQuery] Guid id)
        {
            var result = await _orderDetailService.DelOrderDetailInfo(this.UserId, id);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
    }
}
