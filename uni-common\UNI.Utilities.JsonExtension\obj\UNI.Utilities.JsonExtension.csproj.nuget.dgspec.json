{"format": 1, "restore": {"D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utilities.JsonExtension\\UNI.Utilities.JsonExtension.csproj": {}}, "projects": {"D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utilities.JsonExtension\\UNI.Utilities.JsonExtension.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utilities.JsonExtension\\UNI.Utilities.JsonExtension.csproj", "projectName": "UNI.Utilities.JsonExtension", "projectPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utilities.JsonExtension\\UNI.Utilities.JsonExtension.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\UNI.Utilities.JsonExtension\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\Workspace\\Unicloud\\Backend\\uni-master\\uni-common\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}