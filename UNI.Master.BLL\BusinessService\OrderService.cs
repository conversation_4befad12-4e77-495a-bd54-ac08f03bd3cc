﻿using UNI.Model;
using UNI.Master.Model.UniMaster;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.DAL.Interfaces;
using FilterOrder = UNI.Master.Model.UniMaster.FilterOrder;

namespace UNI.Master.BLL.BusinessService
{
    /// <summary>
    /// Class WebRoleService.
    /// <author></author>
    /// <date>2015/12/02</date>
    /// </summary>
    public class OrderService : IOrderService
    {
        private readonly IOrderRepository _orderRepository;
        public OrderService(IOrderRepository orderRepository)
        {
            if (orderRepository != null)
                _orderRepository = orderRepository;
        }

        public Task<BaseValidate> DelOrderInfo(string userId, Guid id)
        {
            return _orderRepository.DelOrderInfo(userId, id);
        }

        public Task<OrderInfo> GetOrderInfo(string userId, Guid? id)
        {
           return _orderRepository.GetOrderInfo(userId, id);
        }

        public Task<CommonListPage> GetOrderPage(FilterOrder flt)
        {
            return _orderRepository.GetOrderPage(flt);
        }

        public Task<BaseValidate> SetOrderInfo(string userId, OrderInfo prod)
        {
            return _orderRepository.SetOrderInfo(userId, prod);
        }
        public Task<List<CommonValue>> GetOrderList(string customerId, string filter)
        {
            return _orderRepository.GetOrderList(customerId, filter);
        }
    }
}
