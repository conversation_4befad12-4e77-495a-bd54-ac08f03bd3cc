﻿using System;
using System.Threading.Tasks;
using UNI.Master.Model.UniMaster;
using UNI.Model;

namespace UNI.Master.BLL.Interfaces
{
    public interface IOrderDetailService
    {
        Task<CommonListPage> GetOrderDetailPage(FilterOrderDetail flt);
        Task<OrderDetailInfo> GetOrderDetailInfo(string userId, Guid? id);
        Task<BaseValidate> SetOrderDetailInfo(string userId, OrderDetailInfo prod);
        Task<BaseValidate> DelOrderDetailInfo(string userId, Guid id);
    }
}
