﻿using System;
using System.Collections.Generic;
using System.Text;

namespace UNI.Model.APPM
{
    public class userTokenMode
    {
        public string userId { get; set; }
        public int mode { get; set; }
    }
    public class OtpMessageGet : OtpMessageResponse
    {
        //public int tokenType { get; set; }
        public int? code { get; set; }
        public MessageBase optmessage { get; set; }
        public EmailBase otpemail { get; set; }
        public OtpMessageResponse response()
        {
            return new OtpMessageResponse { valid = this.valid, messages = this.messages, secret_cd = this.secret_cd };
        }
    }
    public class OtpMessageResponse
    {
        public bool valid { get; set; }
        public string messages { get; set; }
        public string secret_cd { get; set; }
    }
    public class OtpMessageVerify
    {
        public string secret_cd { get; set; }
        public string otp { get; set; }
    }
    public class WalUserGrant
    {
        public string Phone { get; set; }
        public string Email { get; set; }
        public string UserId { get; set; }
        public int tokenType { get; set; }
        public string sendName { get; set; }
        public string brandName { get; set; }
        public string secret_cd { get; set; } // mã xác thực OTP
        public WalUserGrant(string userId,
            string phone,
            string email,
            int tokenType = 0,
            string brandname = "Sunshine",
            string sendname = "Sunshine Group")
        {
            this.UserId = userId;
            this.Phone = phone;
            this.Email = email;
            this.brandName = brandname;
            this.sendName = sendname;
            this.tokenType = tokenType;
        }
        public WalUserGrant()
        {

        }
    }

}
