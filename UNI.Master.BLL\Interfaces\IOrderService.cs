﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.Model.UniMaster;
using UNI.Model;
using FilterOrder = UNI.Master.Model.UniMaster.FilterOrder;

namespace UNI.Master.BLL.Interfaces
{
    /// <summary>
    /// Interface IAppManagerService
    /// <author></author>
    /// <date>2015/12/02</date>
    /// </summary>
    public interface IOrderService
    {
        Task<CommonListPage> GetOrderPage(FilterOrder flt);
        Task<OrderInfo> GetOrderInfo(string userId, Guid? id);
        Task<BaseValidate> SetOrderInfo(string userId, OrderInfo prod);
        Task<BaseValidate> DelOrderInfo(string userId, Guid id);
        Task<List<CommonValue>> GetOrderList(string userId, string filter);

    }
}
