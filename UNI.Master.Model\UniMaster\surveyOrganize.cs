﻿using UNI.Model;
using System;

namespace UNI.Master.Model.UniMaster
{
    public class surveyOrganize
    {
    }
    public class SurveyOrganizePage : viewBasePage<object>
    {

    }
    public class SurveyOrganizeInfo : viewBaseInfo
    {
        public Guid? Id { get; set; }
    }
    public class SurveyOrganizeRequestModel : FilterBase
    {

    }
    public class surveyOrganize_category
    {
        public Guid value { get; set; }
        public string name { get; set; }
    }
}
